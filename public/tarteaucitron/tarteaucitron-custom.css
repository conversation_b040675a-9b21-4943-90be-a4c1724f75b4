/**
 * * <PERSON><PERSON> personalizzati per tarteaucitron.js
 * Integrazione con i colori del brand Atlas (Blu Ottanio #006A71)
 */

/* Variabili CSS per i colori del brand */
:root {
	--tac-primary: #006A71; /* Blu Ottanio */
	--tac-primary-dark: #004d54; /* Blu Ottanio scuro */
	--tac-primary-light: #e6f3f4; /* Blu Ottanio chiaro */
	--tac-background: #F8F7F4; /* Warm white */
	--tac-text: #212121; /* Dark gray */
	--tac-text-light: #666666; /* Gray medio */
	--tac-border: #e5e5e5; /* Border grigio chiaro */
	--tac-shadow: rgba(0, 106, 113, 0.1); /* Ombra con primary */
	--tac-success: #10b981; /* Verde per stati positivi */
	--tac-warning: #f59e0b; /* Arancione per warning */
	--tac-error: #ef4444; /* Rosso per errori */
}

/* Banner principale */
#tarteaucitronAlertBig {
	background-color: var(--tac-background) !important;
	border: 2px solid var(--tac-primary) !important;
	border-radius: 12px !important;
	box-shadow: 0 8px 32px var(--tac-shadow) !important;
	font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
	max-width: 500px !important;
	margin: 0 auto !important;
	padding: 24px !important;
}

/* Testo del banner */
#tarteaucitronAlertBig .tarteaucitronH1 {
	color: var(--tac-text) !important;
	font-size: 18px !important;
	font-weight: 600 !important;
	margin-bottom: 16px !important;
	line-height: 1.4 !important;
}

#tarteaucitronAlertBig .tarteaucitronH2 {
	color: var(--tac-text-light) !important;
	font-size: 14px !important;
	font-weight: 400 !important;
	line-height: 1.5 !important;
	margin-bottom: 20px !important;
}

/* Pulsanti del banner */
#tarteaucitronAlertBig button {
	border-radius: 8px !important;
	font-weight: 500 !important;
	font-size: 14px !important;
	padding: 12px 20px !important;
	margin: 4px 6px !important;
	border: none !important;
	cursor: pointer !important;
	transition: all 0.2s ease !important;
	font-family: inherit !important;
}

/* Pulsante Accetta tutto */
#tarteaucitronAlertBig #tarteaucitronPersonalize,
#tarteaucitronAlertBig .tarteaucitronAllow {
	background-color: var(--tac-primary) !important;
	color: white !important;
}

#tarteaucitronAlertBig #tarteaucitronPersonalize:hover,
#tarteaucitronAlertBig .tarteaucitronAllow:hover {
	background-color: var(--tac-primary-dark) !important;
	transform: translateY(-1px) !important;
	box-shadow: 0 4px 12px var(--tac-shadow) !important;
}

/* Pulsante Rifiuta tutto */
#tarteaucitronAlertBig #tarteaucitronCloseAlert,
#tarteaucitronAlertBig .tarteaucitronDeny {
	background-color: transparent !important;
	color: var(--tac-text) !important;
	border: 2px solid var(--tac-border) !important;
}

#tarteaucitronAlertBig #tarteaucitronCloseAlert:hover,
#tarteaucitronAlertBig .tarteaucitronDeny:hover {
	background-color: var(--tac-border) !important;
	border-color: var(--tac-text-light) !important;
}

/* Pulsante Personalizza */
#tarteaucitronAlertBig .tarteaucitronPersonalize2 {
	background-color: transparent !important;
	color: var(--tac-primary) !important;
	border: 2px solid var(--tac-primary) !important;
}

#tarteaucitronAlertBig .tarteaucitronPersonalize2:hover {
	background-color: var(--tac-primary-light) !important;
}

/* Pannello di gestione dettagliata */
#tarteaucitron {
	font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

#tarteaucitron .tarteaucitronBorder {
	background-color: var(--tac-background) !important;
	border: 2px solid var(--tac-primary) !important;
	border-radius: 12px !important;
	box-shadow: 0 12px 48px var(--tac-shadow) !important;
}

/* Header del pannello */
#tarteaucitron .tarteaucitronH1 {
	background-color: var(--tac-primary) !important;
	color: white !important;
	font-size: 20px !important;
	font-weight: 600 !important;
	padding: 20px !important;
	margin: 0 !important;
	border-radius: 10px 10px 0 0 !important;
}

/* Contenuto del pannello */
#tarteaucitron .tarteaucitronH2 {
	color: var(--tac-text) !important;
	font-size: 16px !important;
	font-weight: 500 !important;
	margin: 16px 0 8px 0 !important;
}

#tarteaucitron .tarteaucitronH3 {
	color: var(--tac-text-light) !important;
	font-size: 14px !important;
	font-weight: 400 !important;
	line-height: 1.5 !important;
}

/* Servizi nel pannello */
#tarteaucitron .tarteaucitronLine {
	border-bottom: 1px solid var(--tac-border) !important;
	padding: 16px 20px !important;
}

#tarteaucitron .tarteaucitronLine:last-child {
	border-bottom: none !important;
}

/* Toggle switches */
#tarteaucitron .tarteaucitronAsk {
	background-color: var(--tac-border) !important;
	border-radius: 20px !important;
	position: relative !important;
	width: 50px !important;
	height: 24px !important;
	cursor: pointer !important;
	transition: all 0.3s ease !important;
}

#tarteaucitron .tarteaucitronIsAllowed .tarteaucitronAsk {
	background-color: var(--tac-primary) !important;
}

#tarteaucitron .tarteaucitronAsk::after {
	content: '' !important;
	position: absolute !important;
	top: 2px !important;
	left: 2px !important;
	width: 20px !important;
	height: 20px !important;
	background-color: white !important;
	border-radius: 50% !important;
	transition: all 0.3s ease !important;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

#tarteaucitron .tarteaucitronIsAllowed .tarteaucitronAsk::after {
	transform: translateX(26px) !important;
}

/* Icona floating */
#tarteaucitronIcon {
	background-color: var(--tac-primary) !important;
	border-radius: 50% !important;
	box-shadow: 0 4px 16px var(--tac-shadow) !important;
	width: 50px !important;
	height: 50px !important;
	transition: all 0.3s ease !important;
}

#tarteaucitronIcon:hover {
	background-color: var(--tac-primary-dark) !important;
	transform: scale(1.1) !important;
	box-shadow: 0 6px 20px var(--tac-shadow) !important;
}

/* Link e testi */
#tarteaucitron a {
	color: var(--tac-primary) !important;
	text-decoration: underline !important;
	transition: color 0.2s ease !important;
}

#tarteaucitron a:hover {
	color: var(--tac-primary-dark) !important;
}

/* Stati dei servizi */
#tarteaucitron .tarteaucitronIsAllowed .tarteaucitronH3 {
	color: var(--tac-success) !important;
}

#tarteaucitron .tarteaucitronIsDenied .tarteaucitronH3 {
	color: var(--tac-error) !important;
}

#tarteaucitron .tarteaucitronIsWaiting .tarteaucitronH3 {
	color: var(--tac-warning) !important;
}

/* Responsive design */
@media (max-width: 768px) {
	#tarteaucitronAlertBig {
		margin: 16px !important;
		padding: 20px !important;
		max-width: calc(100% - 32px) !important;
	}
	
	#tarteaucitronAlertBig button {
		width: 100% !important;
		margin: 4px 0 !important;
	}
	
	#tarteaucitron .tarteaucitronBorder {
		margin: 10px !important;
		max-width: calc(100% - 20px) !important;
	}
}

/* Animazioni */
@keyframes tarteaucitronFadeIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

#tarteaucitronAlertBig,
#tarteaucitron {
	animation: tarteaucitronFadeIn 0.3s ease-out !important;
}

/* Overlay */
#tarteaucitronBack {
	background-color: rgba(0, 0, 0, 0.5) !important;
	backdrop-filter: blur(4px) !important;
}

/* Accessibilità */
#tarteaucitron button:focus,
#tarteaucitronAlertBig button:focus {
	outline: 2px solid var(--tac-primary) !important;
	outline-offset: 2px !important;
}

/* Stili per cookie obbligatori */
#tarteaucitron .tarteaucitronMandatory {
	background-color: var(--tac-primary-light) !important;
	border-left: 4px solid var(--tac-primary) !important;
}
